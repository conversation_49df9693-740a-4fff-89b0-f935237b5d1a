{
  "$schema": "https://github.com/fastfetch-cli/fastfetch/raw/dev/doc/json_schema.json",
  "logo": {
    "type": "small",
    "source": "fedora_small",
    "color": ["blue", "cyan"],
    "padding": {
      "top": 1,
      "left": 2,
      "right": 2
    }
  },
  "display": {
    "separator": " ❯ ",
    "color": "cyan",
    "keyWidth": 12
  },
  "modules": [
    "title",
    {
      "type": "os",
      "key": "OS",
      "keyIcon": "\uF30F" // Fedora icon from Nerd Fonts
    },
    {
      "type": "kernel",
      "key": "Kernel",
      "keyIcon": "\uF109"
    },
    {
      "type": "cpu",
      "key": "CPU",
      "keyIcon": "\uF85a",
      "format": "{name} ({cores-physical}C/{cores-logical}T)"
    },
    {
      "type": "memory",
      "key": "Memory",
      "keyIcon": "\uF2db",
      "format": "{used:.2f} GiB / {total:.2f} GiB"
    },
    {
      "type": "uptime",
      "key": "Uptime",
      "keyIcon": "\uF2d2"
    },
    {
      "type": "custom",
      "key": "Shell",
      "keyIcon": "\uF489",
      "command": "zsh --version | cut -d ' ' -f 2"
    }
  ]
}